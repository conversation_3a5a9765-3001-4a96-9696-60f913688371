use serde::{Deserialize, Serialize};
use std::hash::{Hash, Hasher};

/// Aggregation key for site-level test item grouping
/// Extends program-level grouping with site dimension
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub struct TestItemSiteKey {
    pub customer: String,
    pub device_id: String,
    pub lot_id: String,
    pub test_program: String,
    pub test_num: i64,
    pub test_txt: String,
    pub test_item: String,
    pub site: Option<i64>,
}

impl Hash for TestItemSiteKey {
    fn hash<H: Hasher>(&self, state: &mut H) {
        self.customer.hash(state);
        self.device_id.hash(state);
        self.lot_id.hash(state);
        self.test_program.hash(state);
        self.test_num.hash(state);
        self.test_txt.hash(state);
        self.test_item.hash(state);
        self.site.hash(state);
    }
}

impl TestItemSiteKey {
    /// Creates a new TestItemSiteKey
    pub fn new(
        customer: String,
        device_id: String,
        lot_id: String,
        test_program: String,
        test_num: i64,
        test_txt: String,
        test_item: String,
        site: Option<i64>,
    ) -> Self {
        Self {
            customer,
            device_id,
            lot_id,
            test_program,
            test_num,
            test_txt,
            test_item,
            site,
        }
    }
}