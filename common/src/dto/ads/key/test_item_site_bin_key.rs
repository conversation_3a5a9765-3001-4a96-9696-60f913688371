use serde::{Deserialize, Serialize};
use std::hash::{Hash, Hasher};

/// Aggregation key for site-bin combined test item grouping
/// Combines both site and bin dimensions for comprehensive grouping
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub struct TestItemSiteBinKey {
    pub customer: String,
    pub device_id: String,
    pub lot_id: String,
    pub test_program: String,
    pub test_num: i64,
    pub test_txt: String,
    pub test_item: String,
    pub site: Option<i64>,
    pub hbin: Option<String>,
    pub sbin: Option<String>,
    pub hbin_num: Option<i64>,
    pub hbin_pf: String,
    pub sbin_pf: String,
}

impl Hash for TestItemSiteBinKey {
    fn hash<H: Hasher>(&self, state: &mut H) {
        self.customer.hash(state);
        self.device_id.hash(state);
        self.lot_id.hash(state);
        self.test_program.hash(state);
        self.test_num.hash(state);
        self.test_txt.hash(state);
        self.test_item.hash(state);
        self.site.hash(state);
        self.hbin.hash(state);
        self.sbin.hash(state);
        self.hbin_num.hash(state);
        self.hbin_pf.hash(state);
        self.sbin_pf.hash(state);
    }
}

impl TestItemSiteBinKey {
    /// Creates a new TestItemSiteBinKey
    pub fn new(
        customer: String,
        device_id: String,
        lot_id: String,
        test_program: String,
        test_num: i64,
        test_txt: String,
        test_item: String,
        site: Option<i64>,
        hbin: Option<String>,
        sbin: Option<String>,
        hbin_num: Option<i64>,
        hbin_pf: String,
        sbin_pf: String,
    ) -> Self {
        Self {
            customer,
            device_id,
            lot_id,
            test_program,
            test_num,
            test_txt,
            test_item,
            site,
            hbin,
            sbin,
            hbin_num,
            hbin_pf,
            sbin_pf,
        }
    }
}