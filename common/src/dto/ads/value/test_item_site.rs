use crate::utils::decimal::Decimal38_18;
use chrono::{DateTime, Utc};
use clickhouse::Row;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;

/// ADS TestItemSite aggregation result structure
/// Contains statistical analysis aggregated at the site level
/// Corresponds to ads_yms_stage_test_item_program_site_cluster table
#[derive(Debug, <PERSON>lone, Serialize, Deserialize, Row, PartialEq)]
#[allow(non_snake_case)]
pub struct TestItemSite {
    // Data source and upload information
    pub DATA_SOURCE: Arc<str>,
    pub UPLOAD_TYPE: Arc<str>,
    pub CUSTOMER: Arc<str>,
    pub SUB_CUSTOMER: Arc<str>,

    // Factory and location information
    pub FAB: Arc<str>,
    pub FAB_SITE: Arc<str>,
    pub FACTORY: Arc<str>,
    pub FACTORY_SITE: Arc<str>,
    pub TEST_AREA: Arc<str>,
    pub TEST_STAGE: Arc<str>,

    // Product and lot information
    pub DEVICE_ID: Arc<str>,
    pub LOT_TYPE: Arc<str>,
    pub LOT_ID: Arc<str>,
    pub SBLOT_ID: Arc<str>,
    pub WAFER_ID: Arc<str>,
    pub WAFER_NO: Arc<str>,
    pub WAFER_LOT_ID: Arc<str>,
    pub PRODUCT: Arc<str>,
    pub PRODUCT_TYPE: Arc<str>,
    pub PRODUCT_FAMILY: Arc<str>,

    // Test program information
    pub TEST_PROGRAM: Arc<str>,
    pub TEST_PROGRAM_VERSION: Arc<str>,
    pub TEST_NUM: Option<u32>,
    pub TEST_TXT: Arc<str>,
    pub TEST_ITEM: Arc<str>,

    // Site information
    pub SITE: Option<u32>,

    // Processing flags
    pub IS_PASS_ONLY: u8,
    pub IS_FINAL: u8,

    // List fields for aggregated data
    pub UNITS_LIST: Arc<str>,
    pub ORIGIN_UNITS_LIST: Arc<str>,
    pub LO_LIMIT_LIST: Arc<str>,
    pub HI_LIMIT_LIST: Arc<str>,
    pub ORIGIN_LO_LIMIT_LIST: Arc<str>,
    pub ORIGIN_HI_LIMIT_LIST: Arc<str>,
    pub PROCESS_LIST: Arc<str>,
    pub TESTITEM_TYPE_LIST: Arc<str>,
    pub TEST_TEMPERATURE_LIST: Arc<str>,
    pub TESTER_NAME_LIST: Arc<str>,
    pub TESTER_TYPE_LIST: Arc<str>,
    pub PROBER_HANDLER_TYP_LIST: Arc<str>,
    pub PROBER_HANDLER_ID_LIST: Arc<str>,
    pub PROBECARD_LOADBOARD_TYP_LIST: Arc<str>,
    pub PROBECARD_LOADBOARD_ID_LIST: Arc<str>,
    pub SITE_CNT_LIST: Arc<str>,
    pub SITE_NUMS_LIST: Arc<str>,

    // Count fields
    pub INPUT_CNT: i32,
    pub PASS_CNT: i32,
    pub FAIL_CNT: i32,
    pub PASSBIN_FAILINGITEM_CNT: i32,
    pub EXE_INPUT_CNT: i32,
    pub EXE_PASS_CNT: i32,
    pub EXE_FAIL_CNT: i32,

    // Statistical metrics - basic
    pub MEDIAN: Option<Decimal38_18>,
    pub MEAN: Option<Decimal38_18>,
    pub MAX: Option<Decimal38_18>,
    pub MIN: Option<Decimal38_18>,
    pub MAX_WO_OUTLIERS: Option<Decimal38_18>,
    pub MIN_WO_OUTLIERS: Option<Decimal38_18>,
    pub SUM_SQ: Option<Decimal38_18>,
    pub SUM_VALUE: Option<Decimal38_18>,
    pub STDEV_P: Option<Decimal38_18>,
    pub STDEV_S: Option<Decimal38_18>,
    pub RANGE: Option<Decimal38_18>,
    pub IQR: Option<Decimal38_18>,

    // Quantiles
    pub Q1: Option<Decimal38_18>,
    pub Q3: Option<Decimal38_18>,
    pub LOWER: Option<Decimal38_18>,
    pub UPPER: Option<Decimal38_18>,
    pub OUTLIER_CNT: u32,
    pub P1: Option<Decimal38_18>,
    pub P5: Option<Decimal38_18>,
    pub P10: Option<Decimal38_18>,
    pub P90: Option<Decimal38_18>,
    pub P95: Option<Decimal38_18>,
    pub P99: Option<Decimal38_18>,

    // Histogram data
    pub GROUP_DETAIL: HashMap<String, u32>,

    // Process capability indices
    pub PP: Option<Decimal38_18>,
    pub PPU: Option<Decimal38_18>,
    pub PPL: Option<Decimal38_18>,
    pub PPK: Option<Decimal38_18>,
    pub CP: Option<Decimal38_18>,
    pub CPU: Option<Decimal38_18>,
    pub CPL: Option<Decimal38_18>,
    pub CPK: Option<Decimal38_18>,
    pub CA: Option<Decimal38_18>,

    // Advanced statistical metrics
    pub SKEWNESS: Option<Decimal38_18>,
    pub KURTOSIS: Option<Decimal38_18>,

    // Normalization metrics
    pub NORMALIZATION_MEDIAN: Option<Decimal38_18>,
    pub NORMALIZATION_MEAN: Option<Decimal38_18>,
    pub NORMALIZATION_MAX: Option<Decimal38_18>,
    pub NORMALIZATION_MIN: Option<Decimal38_18>,
    pub NORMALIZATION_MAX_WO_OUTLIERS: Option<Decimal38_18>,
    pub NORMALIZATION_MIN_WO_OUTLIERS: Option<Decimal38_18>,
    pub NORMALIZATION_IQR: Option<Decimal38_18>,
    pub NORMALIZATION_Q1: Option<Decimal38_18>,
    pub NORMALIZATION_Q3: Option<Decimal38_18>,
    pub NORMALIZATION_LOWER: Option<Decimal38_18>,
    pub NORMALIZATION_UPPER: Option<Decimal38_18>,

    // Timestamps
    #[serde(with = "clickhouse::serde::chrono::datetime::option")]
    pub START_TIME: Option<DateTime<Utc>>,
    pub START_HOUR_KEY: Arc<str>,
    pub START_DAY_KEY: Arc<str>,
    #[serde(with = "clickhouse::serde::chrono::datetime::option")]
    pub END_TIME: Option<DateTime<Utc>>,
    pub END_HOUR_KEY: Arc<str>,
    pub END_DAY_KEY: Arc<str>,

    // System fields
    #[serde(with = "clickhouse::serde::chrono::datetime")]
    pub CREATE_TIME: DateTime<Utc>,
    pub CREATE_HOUR_KEY: Arc<str>,
    pub CREATE_DAY_KEY: Arc<str>,
    pub CREATE_USER: Arc<str>,
    #[serde(with = "clickhouse::serde::chrono::datetime")]
    pub UPLOAD_TIME: DateTime<Utc>,
    pub VERSION: i64,
    pub IS_DELETE: u8,
}

impl TestItemSite {
    /// Creates a new TestItemSite with default values
    pub fn new() -> Self {
        let now = chrono::Utc::now();
        Self {
            DATA_SOURCE: Arc::from(""),
            UPLOAD_TYPE: Arc::from(""),
            CUSTOMER: Arc::from(""),
            SUB_CUSTOMER: Arc::from(""),
            FAB: Arc::from(""),
            FAB_SITE: Arc::from(""),
            FACTORY: Arc::from(""),
            FACTORY_SITE: Arc::from(""),
            TEST_AREA: Arc::from(""),
            TEST_STAGE: Arc::from(""),
            DEVICE_ID: Arc::from(""),
            LOT_TYPE: Arc::from(""),
            LOT_ID: Arc::from(""),
            SBLOT_ID: Arc::from(""),
            WAFER_ID: Arc::from(""),
            WAFER_NO: Arc::from(""),
            WAFER_LOT_ID: Arc::from(""),
            PRODUCT: Arc::from(""),
            PRODUCT_TYPE: Arc::from(""),
            PRODUCT_FAMILY: Arc::from(""),
            TEST_PROGRAM: Arc::from(""),
            TEST_PROGRAM_VERSION: Arc::from(""),
            TEST_NUM: None,
            TEST_TXT: Arc::from(""),
            TEST_ITEM: Arc::from(""),
            SITE: None,
            IS_PASS_ONLY: 0,
            IS_FINAL: 0,
            UNITS_LIST: Arc::from(""),
            ORIGIN_UNITS_LIST: Arc::from(""),
            LO_LIMIT_LIST: Arc::from(""),
            HI_LIMIT_LIST: Arc::from(""),
            ORIGIN_LO_LIMIT_LIST: Arc::from(""),
            ORIGIN_HI_LIMIT_LIST: Arc::from(""),
            PROCESS_LIST: Arc::from(""),
            TESTITEM_TYPE_LIST: Arc::from(""),
            TEST_TEMPERATURE_LIST: Arc::from(""),
            TESTER_NAME_LIST: Arc::from(""),
            TESTER_TYPE_LIST: Arc::from(""),
            PROBER_HANDLER_TYP_LIST: Arc::from(""),
            PROBER_HANDLER_ID_LIST: Arc::from(""),
            PROBECARD_LOADBOARD_TYP_LIST: Arc::from(""),
            PROBECARD_LOADBOARD_ID_LIST: Arc::from(""),
            SITE_CNT_LIST: Arc::from(""),
            SITE_NUMS_LIST: Arc::from(""),
            INPUT_CNT: 0,
            PASS_CNT: 0,
            FAIL_CNT: 0,
            PASSBIN_FAILINGITEM_CNT: 0,
            EXE_INPUT_CNT: 0,
            EXE_PASS_CNT: 0,
            EXE_FAIL_CNT: 0,
            MEDIAN: None,
            MEAN: None,
            MAX: None,
            MIN: None,
            MAX_WO_OUTLIERS: None,
            MIN_WO_OUTLIERS: None,
            SUM_SQ: None,
            SUM_VALUE: None,
            STDEV_P: None,
            STDEV_S: None,
            RANGE: None,
            IQR: None,
            Q1: None,
            Q3: None,
            LOWER: None,
            UPPER: None,
            OUTLIER_CNT: 0,
            P1: None,
            P5: None,
            P10: None,
            P90: None,
            P95: None,
            P99: None,
            GROUP_DETAIL: HashMap::new(),
            PP: None,
            PPU: None,
            PPL: None,
            PPK: None,
            CP: None,
            CPU: None,
            CPL: None,
            CPK: None,
            CA: None,
            SKEWNESS: None,
            KURTOSIS: None,
            NORMALIZATION_MEDIAN: None,
            NORMALIZATION_MEAN: None,
            NORMALIZATION_MAX: None,
            NORMALIZATION_MIN: None,
            NORMALIZATION_MAX_WO_OUTLIERS: None,
            NORMALIZATION_MIN_WO_OUTLIERS: None,
            NORMALIZATION_IQR: None,
            NORMALIZATION_Q1: None,
            NORMALIZATION_Q3: None,
            NORMALIZATION_LOWER: None,
            NORMALIZATION_UPPER: None,
            START_TIME: None,
            START_HOUR_KEY: Arc::from(""),
            START_DAY_KEY: Arc::from(""),
            END_TIME: None,
            END_HOUR_KEY: Arc::from(""),
            END_DAY_KEY: Arc::from(""),
            CREATE_TIME: now,
            CREATE_HOUR_KEY: Arc::from(""),
            CREATE_DAY_KEY: Arc::from(""),
            CREATE_USER: Arc::from(""),
            UPLOAD_TIME: now,
            VERSION: 1,
            IS_DELETE: 0,
        }
    }

    /// Calculate failure rate as a percentage
    pub fn failure_rate(&self) -> Option<f64> {
        if self.INPUT_CNT > 0 {
            let rate = (self.FAIL_CNT as f64) / (self.INPUT_CNT as f64) * 100.0;
            Some(rate)
        } else {
            None
        }
    }

    /// Calculate pass rate as a percentage
    pub fn pass_rate(&self) -> Option<f64> {
        if self.INPUT_CNT > 0 {
            let rate = (self.PASS_CNT as f64) / (self.INPUT_CNT as f64) * 100.0;
            Some(rate)
        } else {
            None
        }
    }

    /// Check if this record has valid statistical data
    pub fn has_statistical_data(&self) -> bool {
        self.MEAN.is_some() || self.MEDIAN.is_some() || self.STDEV_P.is_some()
    }

    /// Get the total number of histogram buckets
    pub fn histogram_bucket_count(&self) -> usize {
        self.GROUP_DETAIL.len()
    }

    /// Get the total count from histogram
    pub fn histogram_total_count(&self) -> u32 {
        self.GROUP_DETAIL.values().sum()
    }

    /// Check if this is a final test result
    pub fn is_final_test(&self) -> bool {
        self.IS_FINAL == 1
    }

    /// Check if this is pass-only processing
    pub fn is_pass_only(&self) -> bool {
        self.IS_PASS_ONLY == 1
    }

    /// Get site count list from the list string
    pub fn get_site_cnt_list(&self) -> Vec<u32> {
        if self.SITE_CNT_LIST.is_empty() {
            return Vec::new();
        }

        self.SITE_CNT_LIST
            .split(',')
            .filter_map(|s| s.trim().parse::<u32>().ok())
            .collect()
    }

    /// Get site numbers from the list string
    pub fn get_site_nums_list(&self) -> Vec<u32> {
        if self.SITE_NUMS_LIST.is_empty() {
            return Vec::new();
        }

        self.SITE_NUMS_LIST
            .split(',')
            .filter_map(|s| s.trim().parse::<u32>().ok())
            .collect()
    }

    /// Check if a specific site number is included
    pub fn contains_site(&self, site_num: u32) -> bool {
        self.get_site_nums_list().contains(&site_num)
    }

    /// Get the primary site number (first in sorted order)
    pub fn primary_site(&self) -> Option<u32> {
        self.get_site_nums_list().first().copied()
    }

    /// Get unique site count
    pub fn unique_site_count(&self) -> usize {
        self.get_site_nums_list().len()
    }
}

impl Default for TestItemSite {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_new_test_item_site() {
        let site = TestItemSite::new();

        assert_eq!(site.INPUT_CNT, 0);
        assert_eq!(site.PASS_CNT, 0);
        assert_eq!(site.FAIL_CNT, 0);
        assert!(site.SITE.is_none());
        assert!(site.SITE_CNT_LIST.is_empty());
        assert!(site.SITE_NUMS_LIST.is_empty());
        assert!(site.MEDIAN.is_none());
        assert!(site.MEAN.is_none());
        assert!(site.GROUP_DETAIL.is_empty());
        assert!(!site.has_statistical_data());
        assert!(!site.is_final_test());
        assert!(!site.is_pass_only());
    }

    #[test]
    fn test_failure_rate_calculation() {
        let mut site = TestItemSite::new();
        site.INPUT_CNT = 100;
        site.FAIL_CNT = 25;
        site.PASS_CNT = 75;

        let failure_rate = site.failure_rate().unwrap();
        assert_eq!(failure_rate, 25.0);

        let pass_rate = site.pass_rate().unwrap();
        assert_eq!(pass_rate, 75.0);
    }

    #[test]
    fn test_failure_rate_zero_input() {
        let site = TestItemSite::new();
        assert!(site.failure_rate().is_none());
        assert!(site.pass_rate().is_none());
    }

    #[test]
    fn test_has_statistical_data() {
        let mut site = TestItemSite::new();
        assert!(!site.has_statistical_data());

        // Note: We would need to create a proper Decimal38_18 value here
        // For now, just test the logic
        assert!(!site.has_statistical_data());
    }

    #[test]
    fn test_histogram_operations() {
        let mut site = TestItemSite::new();

        // Add some histogram data
        site.GROUP_DETAIL.insert("bucket_1".to_string(), 10);
        site.GROUP_DETAIL.insert("bucket_2".to_string(), 20);
        site.GROUP_DETAIL.insert("bucket_3".to_string(), 30);

        assert_eq!(site.histogram_bucket_count(), 3);
        assert_eq!(site.histogram_total_count(), 60);
    }

    #[test]
    fn test_site_operations() {
        let mut site = TestItemSite::new();

        // Test parsing site numbers from list
        site.SITE_NUMS_LIST = Arc::from("1,2,3,1"); // Duplicates in string

        let site_nums = site.get_site_nums_list();
        assert_eq!(site_nums, vec![1, 2, 3, 1]); // Preserves order from string
        assert!(site.contains_site(2));
        assert!(!site.contains_site(4));
        assert_eq!(site.primary_site(), Some(1));
        assert_eq!(site.unique_site_count(), 4); // Includes duplicates

        // Test site count list
        site.SITE_CNT_LIST = Arc::from("4,8,12");
        let site_cnts = site.get_site_cnt_list();
        assert_eq!(site_cnts, vec![4, 8, 12]);
    }

    #[test]
    fn test_empty_site_operations() {
        let site = TestItemSite::new();

        assert_eq!(site.unique_site_count(), 0);
        assert!(!site.contains_site(1));
        assert_eq!(site.primary_site(), None);
        assert!(site.get_site_cnt_list().is_empty());
        assert!(site.get_site_nums_list().is_empty());
    }

    #[test]
    fn test_flags() {
        let mut site = TestItemSite::new();

        // Test IS_FINAL flag
        site.IS_FINAL = 1;
        assert!(site.is_final_test());

        site.IS_FINAL = 0;
        assert!(!site.is_final_test());

        // Test IS_PASS_ONLY flag
        site.IS_PASS_ONLY = 1;
        assert!(site.is_pass_only());

        site.IS_PASS_ONLY = 0;
        assert!(!site.is_pass_only());
    }

    #[test]
    fn test_serialization() {
        let mut site = TestItemSite::new();
        site.SITE = Some(1);
        site.SITE_NUMS_LIST = Arc::from("1,2");
        site.SITE_CNT_LIST = Arc::from("4,8");

        // Test that the structure can be serialized and deserialized
        let json = serde_json::to_string(&site).unwrap();
        let deserialized: TestItemSite = serde_json::from_str(&json).unwrap();

        // Test key fields instead of full equality due to DateTime precision differences
        assert_eq!(deserialized.SITE, Some(1));
        assert_eq!(deserialized.SITE_NUMS_LIST.as_ref(), "1,2");
        assert_eq!(deserialized.SITE_CNT_LIST.as_ref(), "4,8");
        assert_eq!(deserialized.get_site_nums_list(), vec![1, 2]);
        assert_eq!(deserialized.get_site_cnt_list(), vec![4, 8]);
        assert_eq!(deserialized.VERSION, 1);
        assert_eq!(deserialized.IS_DELETE, 0);
    }
}
